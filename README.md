# Memect Insight Extractor

大模型文档信息提取模块，用于从文档中智能提取结构化信息。

## 功能特性

- **灵活提示词配置**: 支持自定义提示词模板，内置变量替换功能
- **任务标识管理**: 支持提取目的唯一标识，便于任务追踪和版本管理
- **Schema主键支持**: 字段支持主键标识，便于数据合并和去重处理
- **调试模式**: 详细的DEBUG日志输出，包含大模型完整输入输出信息
- **动态提示词工程**: 根据文档内容和配置版本动态构建最优提示词
- **智能文本分割**: 基于token数的智能分割，支持Markdown格式，保持章节结构完整性
- **完全离线运行**: 所有tokenizer文件随库安装，支持OpenAI和Qwen tokenizer，无需联网
- **分段并发处理**: 智能分割超长文档，支持并发处理提升效率
- **多模型支持**: 兼容 OpenAI API 格式的各种大语言模型
- **多类型支持**: 支持对象、数组、字符串、数字、布尔等多种Schema类型的数据提取
- **结果校验**: 基于 JSON Schema 的严格格式和类型校验
- **错误处理**: 完善的错误处理和自动重试机制
- **高度可配置**: 支持灵活的配置管理

## 安装

```bash
# 使用 uv 安装
uv pip install memect-insight-extractor

# 或从源码安装
git clone <repository-url>
cd memect_insight_extractor
make setup
```

## 快速开始

### 基础使用

```python
from memect_insight_extractor import DocumentExtractor, ExtractionConfig

# 配置提取器
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    max_chunk_tokens=2000,      # 基于token数分割
    tokenizer_type="openai"     # 使用OpenAI tokenizer
)

extractor = DocumentExtractor(config)

# 执行提取
result = await extractor.extract(
    text_content="待提取的文档内容...",
    schema={
        "type": "object",
        "properties": {
            "company_name": {"type": "string", "description": "公司名称"},
            "amount": {"type": "number", "description": "金额"}
        }
    }
)

print(result.extracted_data)
```

### 完整功能使用

```python
from memect_insight_extractor import DocumentExtractor, ExtractionConfig, ExtractionRequest

# 启用调试模式的配置
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    debug_mode=True,            # 启用DEBUG模式
    log_level="DEBUG",          # 详细日志
    log_file="extraction.log"   # 输出到文件
)

# 带主键的Schema定义
schema = {
    "type": "object",
    "properties": {
        "contract_id": {"type": "string", "description": "合同编号"},
        "company_name": {"type": "string", "description": "公司名称"},
        "amount": {"type": "number", "description": "金额"}
    },
    "required": ["contract_id"]
}

# 使用完整功能的提取请求
request = ExtractionRequest(
    text_content="合同编号：CT-001，甲方：北京科技公司，金额：100000元",
    schema=schema,
    extraction_purpose_id="contract_analysis_v2.0",  # 任务标识
    prompt_template="你是专业的合同分析专家。请分析文档 {document_id}，提取 {field_names} 信息，共 {field_count} 个字段。",
    document_id="DOC_001"
)

extractor = DocumentExtractor(config)
result = await extractor.extract_from_request(request)

print(f"提取状态: {result.status}")
print(f"提取数据: {result.extracted_data}")
print(f"处理时间: {result.processing_time:.2f}秒")
```

## 核心功能

### 提示词变量替换

支持在提示词模板中使用变量，提供更灵活的提示词配置：

```python
# 支持的变量
variables = {
    "text_content": "文档内容",
    "document_id": "文档ID",
    "extraction_purpose_id": "提取目的ID",
    "field_names": "字段名称列表",
    "field_count": "字段数量",
    "schema_json": "Schema的JSON字符串"
}

# 使用示例
prompt_template = "你是专业的 {extraction_purpose_id} 专家。请分析文档 {document_id}，提取 {field_names} 信息。"
```

### Schema主键支持

```python
from memect_insight_extractor.models.schema_models import SchemaField, ExtractionSchema

# 定义带主键的字段
fields = [
    SchemaField(name="id", type="string", is_primary_key=True),
    SchemaField(name="name", type="string", required=True),
    SchemaField(name="amount", type="number"),
]

schema = ExtractionSchema(name="contract_schema", fields=fields, json_schema={...})
primary_keys = schema.get_primary_key_fields()  # ["id"]
```

### 调试模式

启用调试模式可以获得详细的处理信息：

```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    debug_mode=True,           # 启用调试模式
    log_level="DEBUG",         # 设置日志级别
    log_file="debug.log"       # 输出到文件
)

# 调试信息包括：
# - 完整的提示词内容
# - LLM的原始输入输出
# - Token使用统计
# - 处理过程详情
# - 错误诊断信息
```

### 多种Schema类型支持

支持不同类型的数据提取，LLM返回结果会根据Schema的第一层类型自动解析：

```python
# 对象类型 - 返回字典
schema = {
    "type": "object",
    "properties": {
        "company": {"type": "string"},
        "amount": {"type": "number"}
    }
}
# 结果: {"company": "北京科技公司", "amount": 100000}

# 数组类型 - 返回列表
schema = {
    "type": "array",
    "items": {"type": "string"}
}
# 结果: ["公司A", "公司B", "公司C"]

# 对象数组类型 - 返回对象列表
schema = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "name": {"type": "string"},
            "amount": {"type": "number"}
        }
    }
}
# 结果: [{"name": "公司A", "amount": 100000}, {"name": "公司B", "amount": 200000}]

# 简单类型 - 返回对应类型的值
schema = {"type": "string"}   # 结果: "北京科技公司"
schema = {"type": "number"}   # 结果: 125000.50
schema = {"type": "boolean"}  # 结果: true
```

## 开发

```bash
# 设置开发环境
make setup

# 运行测试
make test

# 代码格式化
make format

# 代码检查
make lint

# 构建包
make build
```

## 文档

详细文档请参考 `docs/` 目录或运行：

```bash
make docs-serve
```

## 许可证

[MIT License](LICENSE)
