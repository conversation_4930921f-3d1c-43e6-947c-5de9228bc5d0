// 应用主要 JavaScript 代码

// 全局变量
let currentTaskId = null;
let pollingInterval = null;
let pdfDoc = null;
let currentPage = 1;
let currentScale = 1.0;
let currentSchemaMode = 'visual';
let schemaFields = [];
let fieldIdCounter = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConfig();
    loadTaskHistory();
    setupEventListeners();

    // 初始化PDF.js
    initializePDFJS();

    // 从 localStorage 恢复当前任务
    const savedTaskId = localStorage.getItem('currentTaskId');
    if (savedTaskId) {
        currentTaskId = savedTaskId;
        checkTaskStatus(savedTaskId);
        showTaskStatusCard();
    }

    // 检查是否有从示例页面传来的Schema
    const selectedSchema = localStorage.getItem('selectedSchema');
    if (selectedSchema) {
        document.getElementById('extraction-schema').value = selectedSchema;
        localStorage.removeItem('selectedSchema');
        showSection('extract');
    } else {
        // 加载上次使用的schema
        loadLastUsedSchema();
    }

    // 检查URL hash
    if (window.location.hash) {
        const section = window.location.hash.substring(1);
        if (['config', 'extract', 'history'].includes(section)) {
            showSection(section);
        }
    }
});

// 设置事件监听器
function setupEventListeners() {
    // 配置表单提交
    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveConfig();
    });

    // 文件选择变化
    document.getElementById('pdf-file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file && file.size > 50 * 1024 * 1024) {
            showAlert('文件大小不能超过 50MB', 'danger');
            e.target.value = '';
            return;
        }

        if (file) {
            loadPDFPreview(file);
        }
    });
}

// 显示指定的界面部分
function showSection(sectionName) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示指定部分
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // 更新导航状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 特殊处理
    if (sectionName === 'history') {
        loadTaskHistory();
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {
        llm_base_url: document.getElementById('llm-base-url').value,
        llm_api_key: document.getElementById('llm-api-key').value,
        llm_model: document.getElementById('llm-model').value,
        llm_temperature: parseFloat(document.getElementById('llm-temperature').value),
        llm_max_tokens: parseInt(document.getElementById('llm-max-tokens').value),
        llm_timeout: 60,
        max_retries: 3,
        retry_delay: 1.0,
        max_chunk_size: parseInt(document.getElementById('max-chunk-size').value),
        chunk_overlap: 200,
        enable_parallel: document.getElementById('enable-parallel').checked,
        max_parallel_chunks: parseInt(document.getElementById('max-parallel-chunks').value)
    };
    
    localStorage.setItem('extractionConfig', JSON.stringify(config));
    showAlert('配置已保存', 'success');
}

// 从 localStorage 加载配置
function loadConfig() {
    const savedConfig = localStorage.getItem('extractionConfig');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        document.getElementById('llm-base-url').value = config.llm_base_url || 'https://api.openai.com/v1';
        document.getElementById('llm-api-key').value = config.llm_api_key || '';
        document.getElementById('llm-model').value = config.llm_model || 'gpt-4';
        document.getElementById('llm-temperature').value = config.llm_temperature || 0.1;
        document.getElementById('llm-max-tokens').value = config.llm_max_tokens || 4000;
        document.getElementById('max-chunk-size').value = config.max_chunk_size || 8000;
        document.getElementById('max-parallel-chunks').value = config.max_parallel_chunks || 5;
        document.getElementById('enable-parallel').checked = config.enable_parallel !== false;
    }
}

// 初始化PDF.js
function initializePDFJS() {
    if (typeof pdfjsLib !== 'undefined') {
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    }
}

// 加载PDF预览
async function loadPDFPreview(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const loadingTask = pdfjsLib.getDocument(arrayBuffer);
        pdfDoc = await loadingTask.promise;

        currentPage = 1;
        currentScale = 1.0;

        await renderPage(currentPage);

        // 显示预览卡片
        document.getElementById('pdf-preview-card').style.display = 'block';

        // 更新页面信息
        updatePageInfo();

        showAlert('PDF 预览加载成功', 'success');

    } catch (error) {
        console.error('PDF预览加载失败:', error);
        showAlert('PDF 预览加载失败', 'danger');
    }
}

// 渲染PDF页面
async function renderPage(pageNum) {
    if (!pdfDoc) return;

    try {
        const page = await pdfDoc.getPage(pageNum);
        const canvas = document.getElementById('pdf-canvas');
        const context = canvas.getContext('2d');

        const viewport = page.getViewport({ scale: currentScale });
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };

        await page.render(renderContext).promise;

    } catch (error) {
        console.error('PDF页面渲染失败:', error);
    }
}

// 更新页面信息
function updatePageInfo() {
    if (pdfDoc) {
        document.getElementById('page-info').textContent =
            `第 ${currentPage} 页，共 ${pdfDoc.numPages} 页`;
    }
}

// PDF控制函数
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        renderPage(currentPage);
        updatePageInfo();
    }
}

function nextPage() {
    if (pdfDoc && currentPage < pdfDoc.numPages) {
        currentPage++;
        renderPage(currentPage);
        updatePageInfo();
    }
}

function zoomIn() {
    currentScale += 0.2;
    renderPage(currentPage);
}

function zoomOut() {
    if (currentScale > 0.4) {
        currentScale -= 0.2;
        renderPage(currentPage);
    }
}

// Schema管理功能
function saveCurrentSchema() {
    const schemaInput = document.getElementById('extraction-schema');
    const schemaText = schemaInput.value.trim();

    if (!schemaText) {
        showAlert('请先输入 Schema', 'warning');
        return;
    }

    try {
        // 验证JSON格式
        JSON.parse(schemaText);

        // 保存到历史记录
        saveSchemaToHistory(schemaText);
        showAlert('Schema 已保存到历史记录', 'success');

    } catch (error) {
        showAlert('Schema 格式错误，无法保存', 'danger');
    }
}

function saveSchemaToHistory(schemaText) {
    const history = getSchemaHistory();
    const timestamp = new Date().toISOString();
    const schemaObj = JSON.parse(schemaText);

    // 生成简短描述
    const description = generateSchemaDescription(schemaObj);

    const newEntry = {
        id: Date.now().toString(),
        schema: schemaText,
        description: description,
        timestamp: timestamp,
        used_count: 1
    };

    // 检查是否已存在相同的schema
    const existingIndex = history.findIndex(item => item.schema === schemaText);
    if (existingIndex >= 0) {
        history[existingIndex].used_count++;
        history[existingIndex].timestamp = timestamp;
    } else {
        history.unshift(newEntry);
    }

    // 限制历史记录数量
    if (history.length > 20) {
        history.splice(20);
    }

    localStorage.setItem('schemaHistory', JSON.stringify(history));
}

function getSchemaHistory() {
    const history = localStorage.getItem('schemaHistory');
    return history ? JSON.parse(history) : [];
}

function generateSchemaDescription(schemaObj) {
    if (schemaObj.properties) {
        const keys = Object.keys(schemaObj.properties);
        if (keys.length > 0) {
            return `包含字段: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`;
        }
    }
    return '自定义 Schema';
}

function loadLastUsedSchema() {
    const history = getSchemaHistory();
    if (history.length > 0) {
        // 按使用时间排序，加载最近使用的
        history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        document.getElementById('extraction-schema').value = history[0].schema;
    }
}

function loadSchemaHistory() {
    const history = getSchemaHistory();
    const modal = new bootstrap.Modal(document.getElementById('schema-history-modal'));
    const listContainer = document.getElementById('schema-history-list');

    if (history.length === 0) {
        listContainer.innerHTML = '<div class="text-center text-muted">暂无历史记录</div>';
    } else {
        listContainer.innerHTML = history.map(item => `
            <div class="schema-history-item" onclick="selectSchema('${item.id}')">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <strong>${item.description}</strong>
                        <div class="text-muted small">
                            使用时间: ${new Date(item.timestamp).toLocaleString()}
                            | 使用次数: ${item.used_count}
                        </div>
                        <div class="schema-preview">${item.schema.substring(0, 200)}${item.schema.length > 200 ? '...' : ''}</div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); deleteSchema('${item.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    modal.show();
}

function selectSchema(schemaId) {
    const history = getSchemaHistory();
    const schema = history.find(item => item.id === schemaId);

    if (schema) {
        document.getElementById('extraction-schema').value = schema.schema;

        // 更新使用次数和时间
        schema.used_count++;
        schema.timestamp = new Date().toISOString();
        localStorage.setItem('schemaHistory', JSON.stringify(history));

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('schema-history-modal'));
        modal.hide();

        showAlert('Schema 已加载', 'success');
    }
}

function deleteSchema(schemaId) {
    const history = getSchemaHistory();
    const filteredHistory = history.filter(item => item.id !== schemaId);
    localStorage.setItem('schemaHistory', JSON.stringify(filteredHistory));

    // 重新加载历史记录
    loadSchemaHistory();
    showAlert('Schema 已删除', 'success');
}

// 开始提取
async function startExtraction() {
    const fileInput = document.getElementById('pdf-file');
    const schemaInput = document.getElementById('extraction-schema');

    // 验证输入
    if (!fileInput.files[0]) {
        showAlert('请选择 PDF 文件', 'warning');
        return;
    }

    if (!schemaInput.value.trim()) {
        showAlert('请输入提取 Schema', 'warning');
        return;
    }

    // 验证配置
    const config = JSON.parse(localStorage.getItem('extractionConfig') || '{}');
    if (!config.llm_api_key) {
        showAlert('请先配置 API 密钥', 'warning');
        showSection('config');
        return;
    }
    
    try {
        // 解析 Schema
        const schema = JSON.parse(schemaInput.value);

        // 自动保存schema到历史记录
        saveSchemaToHistory(schemaInput.value);

        // 准备表单数据
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        const extractionRequest = {
            schema: schema,
            config: config,
            prompt_template: document.getElementById('prompt-template').value || null,
            custom_instructions: document.getElementById('custom-instructions').value || null
        };

        formData.append('extraction_request', JSON.stringify(extractionRequest));

        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="loading-spinner"></span> 上传中...';
        button.disabled = true;

        // 发送请求
        const response = await fetch('/api/upload/pdf', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            currentTaskId = result.task_id;
            localStorage.setItem('currentTaskId', currentTaskId);

            showAlert(`文件上传成功，任务 ID: ${result.task_id}`, 'success');

            // 显示任务状态卡片
            showTaskStatusCard();

            // 开始轮询任务状态
            startPolling(result.task_id);

        } else {
            showAlert(`上传失败: ${result.detail || result.message}`, 'danger');
        }

    } catch (error) {
        console.error('提取失败:', error);
        showAlert(`提取失败: ${error.message}`, 'danger');
    } finally {
        // 恢复按钮状态
        const button = document.querySelector('[onclick="startExtraction()"]');
        button.innerHTML = '<i class="bi bi-play-fill"></i> 开始提取';
        button.disabled = false;
    }
}

// 开始轮询任务状态
function startPolling(taskId) {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
    
    pollingInterval = setInterval(() => {
        checkTaskStatus(taskId);
    }, 2000); // 每2秒检查一次
}

// 停止轮询
function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
}

// 检查任务状态
async function checkTaskStatus(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/status`);
        const status = await response.json();
        
        if (response.ok) {
            updateTaskStatus(status);
            
            // 如果任务完成或失败，停止轮询
            if (status.status === 'completed' || status.status === 'failed') {
                stopPolling();
                if (status.status === 'completed') {
                    loadTaskResult(taskId);
                }
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

// 显示任务状态卡片
function showTaskStatusCard() {
    document.getElementById('task-status-card').style.display = 'block';
}

// 隐藏任务状态卡片
function hideTaskStatusCard() {
    document.getElementById('task-status-card').style.display = 'none';
}

// 更新任务状态显示
function updateTaskStatus(status) {
    const content = document.getElementById('task-status-content');
    if (!content) return;

    const statusClass = getStatusClass(status.status);
    const progressBar = status.progress > 0 ?
        `<div class="progress mb-3">
            <div class="progress-bar ${status.status === 'processing' ? 'progress-animated' : ''}"
                 style="width: ${status.progress}%">${status.progress.toFixed(1)}%</div>
         </div>` : '';

    const statusIcon = getStatusIcon(status.status);

    content.innerHTML = `
        <div class="task-status-item status-${status.status}">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex align-items-center">
                    <i class="bi ${statusIcon} me-2"></i>
                    <span class="badge ${statusClass}">${getStatusText(status.status)}</span>
                </div>
                <small class="text-muted">${status.task_id}</small>
            </div>

            ${progressBar}

            <div class="row">
                <div class="col-sm-6">
                    <small class="text-muted">文件名:</small><br>
                    <span>${status.filename || '未知'}</span>
                </div>
                <div class="col-sm-6">
                    <small class="text-muted">创建时间:</small><br>
                    <span>${new Date(status.created_at).toLocaleString()}</span>
                </div>
            </div>

            ${status.message ? `
                <div class="mt-2">
                    <small class="text-muted">消息:</small><br>
                    <span>${status.message}</span>
                </div>
            ` : ''}

            ${status.error_message ? `
                <div class="alert alert-danger mt-2 mb-0">
                    <small><strong>错误:</strong> ${status.error_message}</small>
                </div>
            ` : ''}

            ${status.status === 'completed' ? `
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-success" onclick="loadTaskResult('${status.task_id}')">
                        <i class="bi bi-eye"></i> 查看结果
                    </button>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="downloadResult('${status.task_id}')">
                        <i class="bi bi-download"></i> 下载结果
                    </button>
                </div>
            ` : ''}
        </div>
    `;
}

// 加载任务结果
async function loadTaskResult(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/result`);
        const result = await response.json();

        if (response.ok && result.extracted_data) {
            const content = document.getElementById('task-status-content');
            const currentContent = content.innerHTML;

            // 检查是否已经显示了结果
            if (!currentContent.includes('提取结果:')) {
                content.innerHTML = currentContent + `
                    <hr>
                    <h6><i class="bi bi-check-circle text-success"></i> 提取结果:</h6>
                    <div class="json-viewer">
                        <pre>${JSON.stringify(result.extracted_data, null, 2)}</pre>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('加载任务结果失败:', error);
        showAlert('加载结果失败', 'danger');
    }
}

// 下载结果
async function downloadResult(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/result`);
        const result = await response.json();
        
        if (response.ok) {
            const dataStr = JSON.stringify(result, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `extraction_result_${taskId}.json`;
            link.click();
        }
    } catch (error) {
        console.error('下载结果失败:', error);
        showAlert('下载失败', 'danger');
    }
}

// 加载任务历史
async function loadTaskHistory() {
    try {
        const response = await fetch('/api/tasks/?limit=20');
        const tasks = await response.json();
        
        const container = document.getElementById('task-history-list');
        
        if (tasks.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">暂无任务记录</div>';
            return;
        }
        
        container.innerHTML = tasks.map(task => `
            <div class="task-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <strong class="me-2">${task.filename || '未知文件'}</strong>
                            <span class="badge ${getStatusClass(task.status)}">${getStatusText(task.status)}</span>
                        </div>
                        <div class="file-info">
                            <div>任务 ID: ${task.task_id}</div>
                            <div>创建时间: ${new Date(task.created_at).toLocaleString()}</div>
                            ${task.file_size ? `<div>文件大小: ${formatFileSize(task.file_size)}</div>` : ''}
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewTaskInExtractSection('${task.task_id}')">
                            <i class="bi bi-eye"></i> 查看
                        </button>
                        ${task.status === 'completed' ? 
                            `<button class="btn btn-outline-success btn-sm" onclick="downloadResult('${task.task_id}')">
                                <i class="bi bi-download"></i> 下载
                             </button>` : ''}
                    </div>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('加载任务历史失败:', error);
        document.getElementById('task-history-list').innerHTML = 
            '<div class="alert alert-danger">加载任务历史失败</div>';
    }
}

// 工具函数
function getStatusClass(status) {
    const classes = {
        'pending': 'bg-secondary',
        'processing': 'bg-primary',
        'completed': 'bg-success',
        'failed': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
    };
    return texts[status] || '未知';
}

function getStatusIcon(status) {
    const icons = {
        'pending': 'bi-clock',
        'processing': 'bi-arrow-repeat',
        'completed': 'bi-check-circle',
        'failed': 'bi-x-circle'
    };
    return icons[status] || 'bi-question-circle';
}

// 在提取界面查看任务
function viewTaskInExtractSection(taskId) {
    currentTaskId = taskId;
    localStorage.setItem('currentTaskId', currentTaskId);

    // 切换到提取界面
    showSection('extract');

    // 显示任务状态卡片
    showTaskStatusCard();

    // 检查任务状态
    checkTaskStatus(taskId);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showAlert(message, type = 'info') {
    // 创建警告框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
