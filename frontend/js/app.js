// 应用主要 JavaScript 代码

// 全局变量
let currentTaskId = null;
let pollingInterval = null;
let pdfDoc = null;
let currentPage = 1;
let currentScale = 1.0;
let currentSchemaMode = 'visual';
let schemaFields = [];
let fieldIdCounter = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConfig();
    loadTaskHistory();
    setupEventListeners();

    // 初始化PDF.js
    initializePDFJS();

    // 从 localStorage 恢复当前任务
    const savedTaskId = localStorage.getItem('currentTaskId');
    if (savedTaskId) {
        currentTaskId = savedTaskId;
        checkTaskStatus(savedTaskId);
        showTaskStatusCard();
    }

    // 检查是否有从示例页面传来的Schema
    const selectedSchema = localStorage.getItem('selectedSchema');
    if (selectedSchema) {
        document.getElementById('extraction-schema').value = selectedSchema;
        localStorage.removeItem('selectedSchema');
        showSection('extract');
    } else {
        // 加载上次使用的schema
        loadLastUsedSchema();
    }

    // 初始化可视化编辑器
    initializeVisualEditor();

    // 检查URL hash
    if (window.location.hash) {
        const section = window.location.hash.substring(1);
        if (['config', 'extract', 'history'].includes(section)) {
            showSection(section);
        }
    }
});

// 设置事件监听器
function setupEventListeners() {
    // 配置表单提交
    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveConfig();
    });

    // 文件选择变化
    document.getElementById('pdf-file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file && file.size > 50 * 1024 * 1024) {
            showAlert('文件大小不能超过 50MB', 'danger');
            e.target.value = '';
            return;
        }

        if (file) {
            loadPDFPreview(file);
        }
    });
}

// 显示指定的界面部分
function showSection(sectionName) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示指定部分
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // 更新导航状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 特殊处理
    if (sectionName === 'history') {
        loadTaskHistory();
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {
        llm_base_url: document.getElementById('llm-base-url').value,
        llm_api_key: document.getElementById('llm-api-key').value,
        llm_model: document.getElementById('llm-model').value,
        llm_temperature: parseFloat(document.getElementById('llm-temperature').value),
        llm_max_tokens: parseInt(document.getElementById('llm-max-tokens').value),
        llm_timeout: 60,
        max_retries: 3,
        retry_delay: 1.0,
        max_chunk_size: parseInt(document.getElementById('max-chunk-size').value),
        chunk_overlap: 200,
        enable_parallel: document.getElementById('enable-parallel').checked,
        max_parallel_chunks: parseInt(document.getElementById('max-parallel-chunks').value)
    };
    
    localStorage.setItem('extractionConfig', JSON.stringify(config));
    showAlert('配置已保存', 'success');
}

// 从 localStorage 加载配置
function loadConfig() {
    const savedConfig = localStorage.getItem('extractionConfig');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        document.getElementById('llm-base-url').value = config.llm_base_url || 'https://api.openai.com/v1';
        document.getElementById('llm-api-key').value = config.llm_api_key || '';
        document.getElementById('llm-model').value = config.llm_model || 'gpt-4';
        document.getElementById('llm-temperature').value = config.llm_temperature || 0.1;
        document.getElementById('llm-max-tokens').value = config.llm_max_tokens || 4000;
        document.getElementById('max-chunk-size').value = config.max_chunk_size || 8000;
        document.getElementById('max-parallel-chunks').value = config.max_parallel_chunks || 5;
        document.getElementById('enable-parallel').checked = config.enable_parallel !== false;
    }
}

// 初始化PDF.js
function initializePDFJS() {
    if (typeof pdfjsLib !== 'undefined') {
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    }
}

// 加载PDF预览
async function loadPDFPreview(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const loadingTask = pdfjsLib.getDocument(arrayBuffer);
        pdfDoc = await loadingTask.promise;

        currentPage = 1;
        currentScale = 1.0;

        await renderPage(currentPage);

        // 显示预览卡片
        document.getElementById('pdf-preview-card').style.display = 'block';

        // 更新页面信息
        updatePageInfo();

        showAlert('PDF 预览加载成功', 'success');

    } catch (error) {
        console.error('PDF预览加载失败:', error);
        showAlert('PDF 预览加载失败', 'danger');
    }
}

// 渲染PDF页面
async function renderPage(pageNum) {
    if (!pdfDoc) return;

    try {
        const page = await pdfDoc.getPage(pageNum);
        const canvas = document.getElementById('pdf-canvas');
        const context = canvas.getContext('2d');

        const viewport = page.getViewport({ scale: currentScale });
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };

        await page.render(renderContext).promise;

    } catch (error) {
        console.error('PDF页面渲染失败:', error);
    }
}

// 更新页面信息
function updatePageInfo() {
    if (pdfDoc) {
        document.getElementById('page-info').textContent =
            `第 ${currentPage} 页，共 ${pdfDoc.numPages} 页`;
    }
}

// PDF控制函数
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        renderPage(currentPage);
        updatePageInfo();
    }
}

function nextPage() {
    if (pdfDoc && currentPage < pdfDoc.numPages) {
        currentPage++;
        renderPage(currentPage);
        updatePageInfo();
    }
}

function zoomIn() {
    currentScale += 0.2;
    renderPage(currentPage);
}

function zoomOut() {
    if (currentScale > 0.4) {
        currentScale -= 0.2;
        renderPage(currentPage);
    }
}

// Schema管理功能
function saveCurrentSchema() {
    let schemaText;

    if (currentSchemaMode === 'visual') {
        // 从可视化编辑器生成schema
        const schema = convertVisualFieldsToJson();
        schemaText = JSON.stringify(schema);

        // 验证是否有有效字段
        if (!schema.properties || Object.keys(schema.properties).length === 0) {
            showAlert('请至少添加一个有效的字段', 'warning');
            return;
        }
    } else {
        // 从JSON编辑器获取
        schemaText = document.getElementById('extraction-schema').value.trim();
        if (!schemaText) {
            showAlert('请先输入 Schema', 'warning');
            return;
        }
    }

    try {
        // 验证JSON格式
        JSON.parse(schemaText);

        // 保存到历史记录
        saveSchemaToHistory(schemaText);
        showAlert('Schema 已保存到历史记录', 'success');

    } catch (error) {
        showAlert('Schema 格式错误，无法保存', 'danger');
    }
}

function saveSchemaToHistory(schemaText) {
    const history = getSchemaHistory();
    const timestamp = new Date().toISOString();
    const schemaObj = JSON.parse(schemaText);

    // 生成简短描述
    const description = generateSchemaDescription(schemaObj);

    const newEntry = {
        id: Date.now().toString(),
        schema: schemaText,
        description: description,
        timestamp: timestamp,
        used_count: 1
    };

    // 检查是否已存在相同的schema
    const existingIndex = history.findIndex(item => item.schema === schemaText);
    if (existingIndex >= 0) {
        history[existingIndex].used_count++;
        history[existingIndex].timestamp = timestamp;
    } else {
        history.unshift(newEntry);
    }

    // 限制历史记录数量
    if (history.length > 20) {
        history.splice(20);
    }

    localStorage.setItem('schemaHistory', JSON.stringify(history));
}

function getSchemaHistory() {
    const history = localStorage.getItem('schemaHistory');
    return history ? JSON.parse(history) : [];
}

function generateSchemaDescription(schemaObj) {
    if (schemaObj.properties) {
        const keys = Object.keys(schemaObj.properties);
        if (keys.length > 0) {
            return `包含字段: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`;
        }
    }
    return '自定义 Schema';
}

function loadLastUsedSchema() {
    const history = getSchemaHistory();
    if (history.length > 0) {
        // 按使用时间排序，加载最近使用的
        history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        const lastSchema = history[0].schema;

        // 更新JSON编辑器
        document.getElementById('extraction-schema').value = lastSchema;

        // 如果当前是可视化模式，转换字段
        if (currentSchemaMode === 'visual') {
            try {
                const schemaObj = JSON.parse(lastSchema);
                convertJsonToVisualFields(schemaObj);
            } catch (error) {
                console.error('转换上次使用的schema失败:', error);
            }
        }
    }
}

function loadSchemaHistory() {
    const history = getSchemaHistory();
    const modal = new bootstrap.Modal(document.getElementById('schema-history-modal'));
    const listContainer = document.getElementById('schema-history-list');

    if (history.length === 0) {
        listContainer.innerHTML = '<div class="text-center text-muted">暂无历史记录</div>';
    } else {
        listContainer.innerHTML = history.map(item => `
            <div class="schema-history-item" onclick="selectSchema('${item.id}')">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <strong>${item.description}</strong>
                        <div class="text-muted small">
                            使用时间: ${new Date(item.timestamp).toLocaleString()}
                            | 使用次数: ${item.used_count}
                        </div>
                        <div class="schema-preview">${item.schema.substring(0, 200)}${item.schema.length > 200 ? '...' : ''}</div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); deleteSchema('${item.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    modal.show();
}

function selectSchema(schemaId) {
    const history = getSchemaHistory();
    const schema = history.find(item => item.id === schemaId);

    if (schema) {
        // 更新JSON编辑器
        document.getElementById('extraction-schema').value = schema.schema;

        // 如果当前是可视化模式，转换字段
        if (currentSchemaMode === 'visual') {
            try {
                const schemaObj = JSON.parse(schema.schema);
                convertJsonToVisualFields(schemaObj);
                renderVisualEditor();
            } catch (error) {
                console.error('转换schema失败:', error);
                showAlert('Schema格式错误，无法转换到可视化模式', 'warning');
            }
        }

        // 更新使用次数和时间
        schema.used_count++;
        schema.timestamp = new Date().toISOString();
        localStorage.setItem('schemaHistory', JSON.stringify(history));

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('schema-history-modal'));
        modal.hide();

        showAlert('Schema 已加载', 'success');
    }
}

function deleteSchema(schemaId) {
    const history = getSchemaHistory();
    const filteredHistory = history.filter(item => item.id !== schemaId);
    localStorage.setItem('schemaHistory', JSON.stringify(filteredHistory));

    // 重新加载历史记录
    loadSchemaHistory();
    showAlert('Schema 已删除', 'success');
}

// 开始提取
async function startExtraction() {
    const fileInput = document.getElementById('pdf-file');

    // 验证输入
    if (!fileInput.files[0]) {
        showAlert('请选择 PDF 文件', 'warning');
        return;
    }

    // 获取schema（根据当前模式）
    let schemaText;
    if (currentSchemaMode === 'visual') {
        // 从可视化编辑器生成schema
        const schema = convertVisualFieldsToJson();
        schemaText = JSON.stringify(schema);

        // 验证是否有有效字段
        if (!schema.properties || Object.keys(schema.properties).length === 0) {
            showAlert('请至少添加一个有效的字段', 'warning');
            return;
        }
    } else {
        // 从JSON编辑器获取
        schemaText = document.getElementById('extraction-schema').value.trim();
        if (!schemaText) {
            showAlert('请输入提取 Schema', 'warning');
            return;
        }
    }

    // 验证配置
    const config = JSON.parse(localStorage.getItem('extractionConfig') || '{}');
    if (!config.llm_api_key) {
        showAlert('请先配置 API 密钥', 'warning');
        showSection('config');
        return;
    }
    
    try {
        // 解析 Schema
        const schema = JSON.parse(schemaText);

        // 自动保存schema到历史记录
        saveSchemaToHistory(schemaText);

        // 准备表单数据
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        const extractionRequest = {
            schema: schema,
            config: config,
            prompt_template: document.getElementById('prompt-template').value || null,
            custom_instructions: document.getElementById('custom-instructions').value || null
        };

        formData.append('extraction_request', JSON.stringify(extractionRequest));

        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="loading-spinner"></span> 上传中...';
        button.disabled = true;

        // 发送请求
        const response = await fetch('/api/upload/pdf', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            currentTaskId = result.task_id;
            localStorage.setItem('currentTaskId', currentTaskId);

            showAlert(`文件上传成功，任务 ID: ${result.task_id}`, 'success');

            // 显示任务状态卡片
            showTaskStatusCard();

            // 开始轮询任务状态
            startPolling(result.task_id);

        } else {
            showAlert(`上传失败: ${result.detail || result.message}`, 'danger');
        }

    } catch (error) {
        console.error('提取失败:', error);
        showAlert(`提取失败: ${error.message}`, 'danger');
    } finally {
        // 恢复按钮状态
        const button = document.querySelector('[onclick="startExtraction()"]');
        button.innerHTML = '<i class="bi bi-play-fill"></i> 开始提取';
        button.disabled = false;
    }
}

// 开始轮询任务状态
function startPolling(taskId) {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
    
    pollingInterval = setInterval(() => {
        checkTaskStatus(taskId);
    }, 2000); // 每2秒检查一次
}

// 停止轮询
function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
}

// 检查任务状态
async function checkTaskStatus(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/status`);
        const status = await response.json();
        
        if (response.ok) {
            updateTaskStatus(status);
            
            // 如果任务完成或失败，停止轮询
            if (status.status === 'completed' || status.status === 'failed') {
                stopPolling();
                if (status.status === 'completed') {
                    loadTaskResult(taskId);
                }
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

// 显示任务状态卡片
function showTaskStatusCard() {
    document.getElementById('task-status-card').style.display = 'block';
}

// 隐藏任务状态卡片
function hideTaskStatusCard() {
    document.getElementById('task-status-card').style.display = 'none';
}

// 更新任务状态显示
function updateTaskStatus(status) {
    const content = document.getElementById('task-status-content');
    if (!content) return;

    const statusClass = getStatusClass(status.status);
    const progressBar = status.progress > 0 ?
        `<div class="progress mb-3">
            <div class="progress-bar ${status.status === 'processing' ? 'progress-animated' : ''}"
                 style="width: ${status.progress}%">${status.progress.toFixed(1)}%</div>
         </div>` : '';

    const statusIcon = getStatusIcon(status.status);

    content.innerHTML = `
        <div class="task-status-item status-${status.status}">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex align-items-center">
                    <i class="bi ${statusIcon} me-2"></i>
                    <span class="badge ${statusClass}">${getStatusText(status.status)}</span>
                </div>
                <small class="text-muted">${status.task_id}</small>
            </div>

            ${progressBar}

            <div class="row">
                <div class="col-sm-6">
                    <small class="text-muted">文件名:</small><br>
                    <span>${status.filename || '未知'}</span>
                </div>
                <div class="col-sm-6">
                    <small class="text-muted">创建时间:</small><br>
                    <span>${new Date(status.created_at).toLocaleString()}</span>
                </div>
            </div>

            ${status.message ? `
                <div class="mt-2">
                    <small class="text-muted">消息:</small><br>
                    <span>${status.message}</span>
                </div>
            ` : ''}

            ${status.error_message ? `
                <div class="alert alert-danger mt-2 mb-0">
                    <small><strong>错误:</strong> ${status.error_message}</small>
                </div>
            ` : ''}

            ${status.status === 'completed' ? `
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-success" onclick="loadTaskResult('${status.task_id}')">
                        <i class="bi bi-eye"></i> 查看结果
                    </button>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="downloadResult('${status.task_id}')">
                        <i class="bi bi-download"></i> 下载结果
                    </button>
                </div>
            ` : ''}
        </div>
    `;
}

// 加载任务结果
async function loadTaskResult(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/result`);
        const result = await response.json();

        if (response.ok && result.extracted_data) {
            const content = document.getElementById('task-status-content');
            const currentContent = content.innerHTML;

            // 检查是否已经显示了结果
            if (!currentContent.includes('提取结果:')) {
                content.innerHTML = currentContent + `
                    <hr>
                    <h6><i class="bi bi-check-circle text-success"></i> 提取结果:</h6>
                    <div class="json-viewer">
                        <pre>${JSON.stringify(result.extracted_data, null, 2)}</pre>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('加载任务结果失败:', error);
        showAlert('加载结果失败', 'danger');
    }
}

// 下载结果
async function downloadResult(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/result`);
        const result = await response.json();
        
        if (response.ok) {
            const dataStr = JSON.stringify(result, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `extraction_result_${taskId}.json`;
            link.click();
        }
    } catch (error) {
        console.error('下载结果失败:', error);
        showAlert('下载失败', 'danger');
    }
}

// 加载任务历史
async function loadTaskHistory() {
    try {
        const response = await fetch('/api/tasks/?limit=20');
        const tasks = await response.json();
        
        const container = document.getElementById('task-history-list');
        
        if (tasks.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">暂无任务记录</div>';
            return;
        }
        
        container.innerHTML = tasks.map(task => `
            <div class="task-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <strong class="me-2">${task.filename || '未知文件'}</strong>
                            <span class="badge ${getStatusClass(task.status)}">${getStatusText(task.status)}</span>
                        </div>
                        <div class="file-info">
                            <div>任务 ID: ${task.task_id}</div>
                            <div>创建时间: ${new Date(task.created_at).toLocaleString()}</div>
                            ${task.file_size ? `<div>文件大小: ${formatFileSize(task.file_size)}</div>` : ''}
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewTaskInExtractSection('${task.task_id}')">
                            <i class="bi bi-eye"></i> 查看
                        </button>
                        ${task.status === 'completed' ? 
                            `<button class="btn btn-outline-success btn-sm" onclick="downloadResult('${task.task_id}')">
                                <i class="bi bi-download"></i> 下载
                             </button>` : ''}
                    </div>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('加载任务历史失败:', error);
        document.getElementById('task-history-list').innerHTML = 
            '<div class="alert alert-danger">加载任务历史失败</div>';
    }
}

// 工具函数
function getStatusClass(status) {
    const classes = {
        'pending': 'bg-secondary',
        'processing': 'bg-primary',
        'completed': 'bg-success',
        'failed': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
    };
    return texts[status] || '未知';
}

function getStatusIcon(status) {
    const icons = {
        'pending': 'bi-clock',
        'processing': 'bi-arrow-repeat',
        'completed': 'bi-check-circle',
        'failed': 'bi-x-circle'
    };
    return icons[status] || 'bi-question-circle';
}

// 在提取界面查看任务
function viewTaskInExtractSection(taskId) {
    currentTaskId = taskId;
    localStorage.setItem('currentTaskId', currentTaskId);

    // 切换到提取界面
    showSection('extract');

    // 显示任务状态卡片
    showTaskStatusCard();

    // 检查任务状态
    checkTaskStatus(taskId);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showAlert(message, type = 'info') {
    // 创建警告框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// ==================== Schema 可视化编辑器 ====================

// 初始化可视化编辑器
function initializeVisualEditor() {
    // 如果有现有的JSON schema，转换为可视化字段
    const jsonSchema = document.getElementById('extraction-schema').value;
    if (jsonSchema.trim()) {
        try {
            const schema = JSON.parse(jsonSchema);
            convertJsonToVisualFields(schema);
        } catch (error) {
            console.warn('无法解析现有schema:', error);
        }
    }

    // 如果没有字段，添加一个默认字段
    if (schemaFields.length === 0) {
        addSchemaField();
    }

    renderVisualEditor();
}

// 切换Schema编辑模式
function switchSchemaMode(mode) {
    currentSchemaMode = mode;

    if (mode === 'visual') {
        document.getElementById('visual-schema-editor').style.display = 'block';
        document.getElementById('json-schema-editor').style.display = 'none';

        // 从JSON转换到可视化
        const jsonSchema = document.getElementById('extraction-schema').value;
        if (jsonSchema.trim()) {
            try {
                const schema = JSON.parse(jsonSchema);
                convertJsonToVisualFields(schema);
                renderVisualEditor();
            } catch (error) {
                showAlert('JSON格式错误，无法转换到可视化模式', 'warning');
            }
        }
    } else {
        document.getElementById('visual-schema-editor').style.display = 'none';
        document.getElementById('json-schema-editor').style.display = 'block';

        // 从可视化转换到JSON
        updateJsonFromVisual();
    }
}

// 添加Schema字段
function addSchemaField(parentId = null, type = 'string') {
    const field = {
        id: ++fieldIdCounter,
        name: '',
        type: type,
        description: '',
        required: false,
        parentId: parentId,
        constraints: {}
    };

    schemaFields.push(field);
    renderVisualEditor();

    // 聚焦到新字段的名称输入框
    setTimeout(() => {
        const nameInput = document.querySelector(`[data-field-id="${field.id}"] .field-name-input`);
        if (nameInput) {
            nameInput.focus();
        }
    }, 100);
}

// 删除Schema字段
function removeSchemaField(fieldId) {
    // 删除字段及其所有子字段
    function removeFieldAndChildren(id) {
        schemaFields = schemaFields.filter(field => field.id !== id);
        // 删除子字段
        const childFields = schemaFields.filter(field => field.parentId === id);
        childFields.forEach(child => removeFieldAndChildren(child.id));
    }

    removeFieldAndChildren(fieldId);
    renderVisualEditor();
    updateJsonFromVisual();
}

// 渲染可视化编辑器
function renderVisualEditor() {
    const container = document.getElementById('schema-fields-container');
    const rootFields = schemaFields.filter(field => !field.parentId);

    container.innerHTML = rootFields.map(field => renderSchemaField(field)).join('');

    // 更新JSON预览
    updateJsonFromVisual();
}

// 渲染单个Schema字段
function renderSchemaField(field, level = 0) {
    const childFields = schemaFields.filter(f => f.parentId === field.id);
    const hasChildren = childFields.length > 0;
    const indent = level > 0 ? 'nested' : '';

    return `
        <div class="schema-field-item ${indent}" data-field-id="${field.id}">
            <div class="schema-field-header">
                <div class="d-flex align-items-center gap-2">
                    <span class="field-type-badge field-type-${field.type}">${field.type}</span>
                    <input type="text" class="form-control form-control-sm field-name-input"
                           placeholder="字段名称" value="${field.name}"
                           onchange="updateFieldProperty(${field.id}, 'name', this.value)">
                    ${field.required ? '<span class="badge bg-danger">必需</span>' : ''}
                </div>
                <div class="schema-field-controls">
                    ${field.type === 'object' ? `
                        <button type="button" class="btn btn-sm btn-outline-primary"
                                onclick="addSchemaField(${field.id}, 'string')">
                            <i class="bi bi-plus"></i> 子字段
                        </button>
                    ` : ''}
                    ${field.type === 'array' ? `
                        <button type="button" class="btn btn-sm btn-outline-info"
                                onclick="configureArrayItems(${field.id})">
                            <i class="bi bi-list"></i> 配置项
                        </button>
                    ` : ''}
                    ${(field.type === 'array' && field.constraints && field.constraints.itemType === 'object') ? `
                        <button type="button" class="btn btn-sm btn-outline-success"
                                onclick="addArrayObjectField(${field.id})">
                            <i class="bi bi-plus"></i> 对象字段
                        </button>
                    ` : ''}
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="removeSchemaField(${field.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>

            <div class="schema-field-form">
                <div class="form-group">
                    <label class="form-label">字段类型</label>
                    <select class="form-select form-select-sm"
                            onchange="updateFieldProperty(${field.id}, 'type', this.value)">
                        <option value="string" ${field.type === 'string' ? 'selected' : ''}>字符串</option>
                        <option value="number" ${field.type === 'number' ? 'selected' : ''}>数字</option>
                        <option value="integer" ${field.type === 'integer' ? 'selected' : ''}>整数</option>
                        <option value="boolean" ${field.type === 'boolean' ? 'selected' : ''}>布尔值</option>
                        <option value="array" ${field.type === 'array' ? 'selected' : ''}>数组</option>
                        <option value="object" ${field.type === 'object' ? 'selected' : ''}>对象</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox"
                               ${field.required ? 'checked' : ''}
                               onchange="updateFieldProperty(${field.id}, 'required', this.checked)">
                        <label class="form-check-label">必需字段</label>
                    </div>
                </div>

                <div class="form-group full-width">
                    <label class="form-label">字段描述</label>
                    <input type="text" class="form-control form-control-sm"
                           placeholder="字段描述" value="${field.description || ''}"
                           onchange="updateFieldProperty(${field.id}, 'description', this.value)">
                </div>

                ${renderFieldConstraints(field)}
            </div>

            ${hasChildren ? `
                <div class="mt-3">
                    <h6>子字段:</h6>
                    ${childFields.map(child => renderSchemaField(child, level + 1)).join('')}
                </div>
            ` : ''}
        </div>
    `;
}

// 渲染字段约束
function renderFieldConstraints(field) {
    const constraints = field.constraints || {};

    if (field.type === 'string') {
        return `
            <div class="field-constraints full-width">
                <h6>字符串约束</h6>
                <div class="constraint-group">
                    <div>
                        <label class="form-label">最小长度</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${constraints.minLength || ''}"
                               onchange="updateFieldConstraint(${field.id}, 'minLength', this.value)">
                    </div>
                    <div>
                        <label class="form-label">最大长度</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${constraints.maxLength || ''}"
                               onchange="updateFieldConstraint(${field.id}, 'maxLength', this.value)">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">正则表达式</label>
                    <input type="text" class="form-control form-control-sm"
                           placeholder="^[a-zA-Z0-9]+$" value="${constraints.pattern || ''}"
                           onchange="updateFieldConstraint(${field.id}, 'pattern', this.value)">
                </div>
                <div class="form-group">
                    <label class="form-label">格式</label>
                    <select class="form-select form-select-sm"
                            onchange="updateFieldConstraint(${field.id}, 'format', this.value)">
                        <option value="">无格式限制</option>
                        <option value="date" ${constraints.format === 'date' ? 'selected' : ''}>日期</option>
                        <option value="time" ${constraints.format === 'time' ? 'selected' : ''}>时间</option>
                        <option value="date-time" ${constraints.format === 'date-time' ? 'selected' : ''}>日期时间</option>
                        <option value="email" ${constraints.format === 'email' ? 'selected' : ''}>邮箱</option>
                        <option value="uri" ${constraints.format === 'uri' ? 'selected' : ''}>URI</option>
                    </select>
                </div>
                ${renderEnumConstraint(field)}
            </div>
        `;
    } else if (field.type === 'number' || field.type === 'integer') {
        return `
            <div class="field-constraints full-width">
                <h6>数值约束</h6>
                <div class="constraint-group">
                    <div>
                        <label class="form-label">最小值</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${constraints.minimum || ''}"
                               onchange="updateFieldConstraint(${field.id}, 'minimum', this.value)">
                    </div>
                    <div>
                        <label class="form-label">最大值</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${constraints.maximum || ''}"
                               onchange="updateFieldConstraint(${field.id}, 'maximum', this.value)">
                    </div>
                </div>
                ${renderEnumConstraint(field)}
            </div>
        `;
    } else if (field.type === 'array') {
        return `
            <div class="field-constraints full-width">
                <h6>数组约束</h6>
                <div class="constraint-group">
                    <div>
                        <label class="form-label">最小项数</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${constraints.minItems || ''}"
                               onchange="updateFieldConstraint(${field.id}, 'minItems', this.value)">
                    </div>
                    <div>
                        <label class="form-label">最大项数</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${constraints.maxItems || ''}"
                               onchange="updateFieldConstraint(${field.id}, 'maxItems', this.value)">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">项目类型</label>
                    <select class="form-select form-select-sm"
                            onchange="updateFieldConstraint(${field.id}, 'itemType', this.value)">
                        <option value="string" ${constraints.itemType === 'string' ? 'selected' : ''}>字符串</option>
                        <option value="number" ${constraints.itemType === 'number' ? 'selected' : ''}>数字</option>
                        <option value="integer" ${constraints.itemType === 'integer' ? 'selected' : ''}>整数</option>
                        <option value="boolean" ${constraints.itemType === 'boolean' ? 'selected' : ''}>布尔值</option>
                        <option value="object" ${constraints.itemType === 'object' ? 'selected' : ''}>对象</option>
                    </select>
                </div>
            </div>
        `;
    }

    return '';
}

// 渲染枚举约束
function renderEnumConstraint(field) {
    const constraints = field.constraints || {};
    const enumValues = constraints.enum || [];

    return `
        <div class="form-group">
            <label class="form-label">枚举值</label>
            <div class="enum-values">
                ${enumValues.map(value => `
                    <span class="enum-value">
                        ${value}
                        <span class="remove-enum" onclick="removeEnumValue(${field.id}, '${value}')">×</span>
                    </span>
                `).join('')}
            </div>
            <div class="add-enum-input">
                <input type="text" class="form-control form-control-sm"
                       placeholder="添加枚举值" id="enum-input-${field.id}">
                <button type="button" class="btn btn-sm btn-outline-primary"
                        onclick="addEnumValue(${field.id})">添加</button>
            </div>
        </div>
    `;
}

// 更新字段属性
function updateFieldProperty(fieldId, property, value) {
    const field = schemaFields.find(f => f.id === fieldId);
    if (field) {
        field[property] = value;

        // 如果改变了类型，清除不适用的约束
        if (property === 'type') {
            field.constraints = {};
            renderVisualEditor();
        } else {
            updateJsonFromVisual();
        }
    }
}

// 更新字段约束
function updateFieldConstraint(fieldId, constraint, value) {
    const field = schemaFields.find(f => f.id === fieldId);
    if (field) {
        if (!field.constraints) {
            field.constraints = {};
        }

        if (value === '' || value === null || value === undefined) {
            delete field.constraints[constraint];
        } else {
            // 转换数值类型
            if (['minimum', 'maximum', 'minLength', 'maxLength', 'minItems', 'maxItems'].includes(constraint)) {
                field.constraints[constraint] = parseInt(value) || 0;
            } else {
                field.constraints[constraint] = value;
            }
        }

        updateJsonFromVisual();
    }
}

// 添加枚举值
function addEnumValue(fieldId) {
    const input = document.getElementById(`enum-input-${fieldId}`);
    const value = input.value.trim();

    if (value) {
        const field = schemaFields.find(f => f.id === fieldId);
        if (field) {
            if (!field.constraints) {
                field.constraints = {};
            }
            if (!field.constraints.enum) {
                field.constraints.enum = [];
            }

            if (!field.constraints.enum.includes(value)) {
                field.constraints.enum.push(value);
                input.value = '';
                renderVisualEditor();
            }
        }
    }
}

// 删除枚举值
function removeEnumValue(fieldId, value) {
    const field = schemaFields.find(f => f.id === fieldId);
    if (field && field.constraints && field.constraints.enum) {
        field.constraints.enum = field.constraints.enum.filter(v => v !== value);
        if (field.constraints.enum.length === 0) {
            delete field.constraints.enum;
        }
        renderVisualEditor();
    }
}

// 从可视化字段更新JSON
function updateJsonFromVisual() {
    const schema = convertVisualFieldsToJson();
    document.getElementById('extraction-schema').value = JSON.stringify(schema, null, 2);
}

// 将可视化字段转换为JSON Schema
function convertVisualFieldsToJson() {
    const rootFields = schemaFields.filter(field => !field.parentId && field.name);

    if (rootFields.length === 0) {
        return {
            type: "object",
            properties: {},
            required: []
        };
    }

    const properties = {};
    const required = [];

    rootFields.forEach(field => {
        if (field.name) {
            properties[field.name] = convertFieldToJsonProperty(field);
            if (field.required) {
                required.push(field.name);
            }
        }
    });

    const schema = {
        type: "object",
        properties: properties
    };

    if (required.length > 0) {
        schema.required = required;
    }

    return schema;
}

// 将单个字段转换为JSON属性
function convertFieldToJsonProperty(field) {
    const property = {
        type: field.type
    };

    if (field.description) {
        property.description = field.description;
    }

    // 添加约束
    const constraints = field.constraints || {};
    Object.keys(constraints).forEach(key => {
        if (key === 'itemType') {
            // 处理数组项类型
            if (field.type === 'array') {
                property.items = { type: constraints.itemType };
            }
        } else if (key === 'enum' && constraints.enum && constraints.enum.length > 0) {
            property.enum = constraints.enum;
        } else if (constraints[key] !== undefined && constraints[key] !== '') {
            property[key] = constraints[key];
        }
    });

    // 处理子字段（对象类型）
    if (field.type === 'object') {
        const childFields = schemaFields.filter(f => f.parentId === field.id && f.name);
        if (childFields.length > 0) {
            property.properties = {};
            const childRequired = [];

            childFields.forEach(child => {
                property.properties[child.name] = convertFieldToJsonProperty(child);
                if (child.required) {
                    childRequired.push(child.name);
                }
            });

            if (childRequired.length > 0) {
                property.required = childRequired;
            }
        }
    }

    return property;
}

// 从JSON Schema转换为可视化字段
function convertJsonToVisualFields(schema) {
    schemaFields = [];
    fieldIdCounter = 0;

    if (schema.type === 'object' && schema.properties) {
        Object.keys(schema.properties).forEach(key => {
            const property = schema.properties[key];
            const field = convertJsonPropertyToField(key, property, null, schema.required || []);
            schemaFields.push(field);
        });
    }
}

// 将JSON属性转换为字段
function convertJsonPropertyToField(name, property, parentId, requiredFields) {
    const field = {
        id: ++fieldIdCounter,
        name: name,
        type: property.type || 'string',
        description: property.description || '',
        required: requiredFields.includes(name),
        parentId: parentId,
        constraints: {}
    };

    // 提取约束
    const constraintKeys = ['minimum', 'maximum', 'minLength', 'maxLength', 'minItems', 'maxItems', 'pattern', 'format', 'enum'];
    constraintKeys.forEach(key => {
        if (property[key] !== undefined) {
            field.constraints[key] = property[key];
        }
    });

    // 处理数组项类型
    if (property.type === 'array' && property.items && property.items.type) {
        field.constraints.itemType = property.items.type;
    }

    // 处理子字段（对象类型）
    if (property.type === 'object' && property.properties) {
        Object.keys(property.properties).forEach(childKey => {
            const childProperty = property.properties[childKey];
            const childField = convertJsonPropertyToField(
                childKey,
                childProperty,
                field.id,
                property.required || []
            );
            schemaFields.push(childField);
        });
    }

    return field;
}

// ==================== Schema 模板功能 ====================

// 加载Schema模板
function loadSchemaTemplates() {
    const modal = new bootstrap.Modal(document.getElementById('schema-templates-modal'));
    const categoriesContainer = document.getElementById('template-categories');
    const detailsContainer = document.getElementById('template-details');

    // 模板数据
    const templates = getSchemaTemplates();
    const categories = [...new Set(templates.map(t => t.category))];

    // 渲染分类列表
    categoriesContainer.innerHTML = categories.map(category => `
        <button type="button" class="list-group-item list-group-item-action"
                onclick="showTemplateCategory('${category}')">
            ${category}
        </button>
    `).join('');

    // 默认显示第一个分类
    if (categories.length > 0) {
        showTemplateCategory(categories[0]);
    }

    modal.show();
}

// 显示模板分类
function showTemplateCategory(category) {
    const templates = getSchemaTemplates().filter(t => t.category === category);
    const detailsContainer = document.getElementById('template-details');

    // 更新分类选中状态
    document.querySelectorAll('#template-categories .list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');

    detailsContainer.innerHTML = `
        <h5>${category}</h5>
        <div class="row">
            ${templates.map(template => `
                <div class="col-md-6 mb-3">
                    <div class="template-item" onclick="selectTemplate('${template.id}')">
                        <h6>${template.name}</h6>
                        <p class="text-muted">${template.description}</p>
                        <div class="template-preview">
                            ${JSON.stringify(template.schema, null, 2).substring(0, 200)}...
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// 选择模板
function selectTemplate(templateId) {
    const templates = getSchemaTemplates();
    const template = templates.find(t => t.id === templateId);

    if (template) {
        // 更新选中状态
        document.querySelectorAll('.template-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.target.closest('.template-item').classList.add('selected');

        // 显示模板详情和操作按钮
        const detailsContainer = document.getElementById('template-details');
        const existingActions = detailsContainer.querySelector('.template-actions');

        if (existingActions) {
            existingActions.remove();
        }

        detailsContainer.innerHTML += `
            <div class="template-actions">
                <button type="button" class="btn btn-primary" onclick="applyTemplate('${templateId}')">
                    <i class="bi bi-check"></i> 使用此模板
                </button>
                <button type="button" class="btn btn-secondary" onclick="previewTemplate('${templateId}')">
                    <i class="bi bi-eye"></i> 预览
                </button>
            </div>
        `;
    }
}

// 应用模板
function applyTemplate(templateId) {
    const templates = getSchemaTemplates();
    const template = templates.find(t => t.id === templateId);

    if (template) {
        // 更新JSON编辑器
        document.getElementById('extraction-schema').value = JSON.stringify(template.schema, null, 2);

        // 如果当前是可视化模式，转换字段
        if (currentSchemaMode === 'visual') {
            convertJsonToVisualFields(template.schema);
            renderVisualEditor();
        }

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('schema-templates-modal'));
        modal.hide();

        showAlert(`已应用模板: ${template.name}`, 'success');
    }
}

// 预览模板
function previewTemplate(templateId) {
    const templates = getSchemaTemplates();
    const template = templates.find(t => t.id === templateId);

    if (template) {
        const detailsContainer = document.getElementById('template-details');
        const existingPreview = detailsContainer.querySelector('.full-template-preview');

        if (existingPreview) {
            existingPreview.remove();
        }

        detailsContainer.innerHTML += `
            <div class="full-template-preview mt-3">
                <h6>完整模板预览</h6>
                <div class="template-preview">
                    <pre>${JSON.stringify(template.schema, null, 2)}</pre>
                </div>
            </div>
        `;
    }
}

// 获取Schema模板数据
function getSchemaTemplates() {
    return [
        {
            id: 'contract',
            name: '合同信息提取',
            category: '法律文档',
            description: '提取合同中的基本信息，包括当事方、金额、日期等',
            schema: {
                "type": "object",
                "properties": {
                    "company_name": {"type": "string", "description": "公司名称"},
                    "contract_amount": {"type": "number", "description": "合同金额（万元）"},
                    "contract_date": {"type": "string", "format": "date", "description": "合同签署日期"},
                    "contract_duration": {"type": "string", "description": "合同期限"},
                    "parties": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string", "description": "当事方名称"},
                                "role": {"type": "string", "enum": ["甲方", "乙方"], "description": "角色"},
                                "contact": {"type": "string", "description": "联系方式"}
                            },
                            "required": ["name", "role"]
                        },
                        "description": "合同当事方信息"
                    }
                },
                "required": ["company_name", "contract_amount", "contract_date"]
            }
        },
        {
            id: 'financial',
            name: '财务报表信息',
            category: '财务文档',
            description: '提取财务报表中的关键财务数据和指标',
            schema: {
                "type": "object",
                "properties": {
                    "company_info": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "description": "公司名称"},
                            "stock_code": {"type": "string", "description": "股票代码"},
                            "industry": {"type": "string", "description": "所属行业"}
                        },
                        "required": ["name"],
                        "description": "公司基本信息"
                    },
                    "financial_data": {
                        "type": "object",
                        "properties": {
                            "revenue": {"type": "number", "description": "营业收入（万元）"},
                            "net_profit": {"type": "number", "description": "净利润（万元）"},
                            "total_assets": {"type": "number", "description": "总资产（万元）"}
                        },
                        "description": "财务数据"
                    },
                    "period_info": {
                        "type": "object",
                        "properties": {
                            "year": {"type": "integer", "description": "报告年度"},
                            "quarter": {"type": "string", "enum": ["Q1", "Q2", "Q3", "Q4", "年报"], "description": "报告季度"}
                        },
                        "required": ["year"],
                        "description": "报告期信息"
                    }
                },
                "required": ["company_info", "financial_data", "period_info"]
            }
        },
        {
            id: 'invoice',
            name: '发票信息提取',
            category: '财务文档',
            description: '提取发票中的基本信息',
            schema: {
                "type": "object",
                "properties": {
                    "invoice_number": {"type": "string", "description": "发票号码"},
                    "invoice_date": {"type": "string", "format": "date", "description": "开票日期"},
                    "seller_name": {"type": "string", "description": "销售方名称"},
                    "buyer_name": {"type": "string", "description": "购买方名称"},
                    "total_amount": {"type": "number", "description": "价税合计"},
                    "tax_amount": {"type": "number", "description": "税额"},
                    "items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string", "description": "商品名称"},
                                "quantity": {"type": "number", "description": "数量"},
                                "unit_price": {"type": "number", "description": "单价"},
                                "amount": {"type": "number", "description": "金额"}
                            }
                        },
                        "description": "商品明细"
                    }
                },
                "required": ["invoice_number", "invoice_date", "total_amount"]
            }
        },
        {
            id: 'resume',
            name: '简历信息提取',
            category: '人力资源',
            description: '提取简历中的个人信息、教育背景、工作经历等',
            schema: {
                "type": "object",
                "properties": {
                    "personal_info": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "description": "姓名"},
                            "age": {"type": "integer", "description": "年龄"},
                            "phone": {"type": "string", "description": "电话"},
                            "email": {"type": "string", "format": "email", "description": "邮箱"},
                            "address": {"type": "string", "description": "地址"}
                        },
                        "required": ["name"],
                        "description": "个人基本信息"
                    },
                    "education": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "school": {"type": "string", "description": "学校名称"},
                                "degree": {"type": "string", "description": "学历"},
                                "major": {"type": "string", "description": "专业"},
                                "graduation_year": {"type": "integer", "description": "毕业年份"}
                            }
                        },
                        "description": "教育背景"
                    },
                    "work_experience": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "company": {"type": "string", "description": "公司名称"},
                                "position": {"type": "string", "description": "职位"},
                                "start_date": {"type": "string", "description": "开始日期"},
                                "end_date": {"type": "string", "description": "结束日期"},
                                "description": {"type": "string", "description": "工作描述"}
                            }
                        },
                        "description": "工作经历"
                    }
                },
                "required": ["personal_info"]
            }
        }
    ];
}
