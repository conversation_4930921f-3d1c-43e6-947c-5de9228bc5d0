/* 自定义样式 */

body {
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
}

.section {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.progress {
    height: 8px;
}

.task-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    transition: box-shadow 0.15s ease-in-out;
}

.task-item:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #6c757d;
}

.status-processing {
    background-color: #0d6efd;
}

.status-completed {
    background-color: #198754;
}

.status-failed {
    background-color: #dc3545;
}

.file-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.json-viewer {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    max-height: 400px;
    overflow-y: auto;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert-custom {
    border-radius: 0.5rem;
    border: none;
    padding: 1rem 1.5rem;
}

.btn-custom {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.navbar-nav .nav-link {
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: background-color 0.15s ease-in-out;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #0c63e4;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.text-muted {
    color: #6c757d !important;
}

.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #fd7e14 !important;
}

.text-info {
    color: #0dcaf0 !important;
}

/* PDF预览样式 */
.pdf-preview-container {
    position: relative;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    overflow: hidden;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
}

#pdf-canvas {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    height: auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-controls {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 0.375rem;
    padding: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pdf-controls button {
    border: none;
    background: transparent;
    padding: 0.25rem 0.5rem;
}

.pdf-controls button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

#page-info {
    font-size: 0.875rem;
    color: #6c757d;
    white-space: nowrap;
}

/* 任务状态卡片样式 */
.task-status-item {
    border-left: 4px solid #dee2e6;
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.task-status-item.status-pending {
    border-left-color: #6c757d;
}

.task-status-item.status-processing {
    border-left-color: #0d6efd;
}

.task-status-item.status-completed {
    border-left-color: #198754;
}

.task-status-item.status-failed {
    border-left-color: #dc3545;
}

.progress-animated {
    background: linear-gradient(45deg,
        rgba(255,255,255,.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255,255,255,.15) 50%,
        rgba(255,255,255,.15) 75%,
        transparent 75%,
        transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* Schema历史样式 */
.schema-history-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.schema-history-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 0.125rem 0.25rem rgba(13, 110, 253, 0.25);
}

.schema-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    max-height: 100px;
    overflow-y: auto;
    margin-top: 0.5rem;
}

/* 可视化Schema编辑器样式 */
.schema-editor {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

.schema-fields-container {
    max-height: 400px;
    overflow-y: auto;
}

.schema-field-item {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.schema-field-item.nested {
    margin-left: 1.5rem;
    border-left: 3px solid #0d6efd;
}

.schema-field-item.array-object-field {
    margin-left: 1.5rem;
    border-left: 3px solid #f57c00;
    background-color: #fff8e1;
}

.schema-field-header {
    display: flex;
    justify-content-between;
    align-items-center;
    margin-bottom: 0.75rem;
}

.schema-field-basic {
    margin-bottom: 0.5rem;
}

.schema-field-details {
    border-top: 1px solid #dee2e6;
    padding-top: 0.75rem;
    margin-top: 0.5rem;
}

.field-toggle {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.schema-field-controls {
    display: flex;
    gap: 0.5rem;
}

.field-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.field-type-string { background-color: #e3f2fd; color: #1976d2; }
.field-type-number { background-color: #f3e5f5; color: #7b1fa2; }
.field-type-integer { background-color: #f3e5f5; color: #7b1fa2; }
.field-type-boolean { background-color: #e8f5e8; color: #388e3c; }
.field-type-array { background-color: #fff3e0; color: #f57c00; }
.field-type-object { background-color: #fce4ec; color: #c2185b; }

.field-constraints {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 0.75rem;
}

.field-constraints .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    color: #6c757d;
}

.enum-values {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.enum-value {
    background-color: #e3f2fd;
    border: 1px solid #90caf9;
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.enum-value .remove-enum {
    cursor: pointer;
    color: #d32f2f;
    font-weight: bold;
    margin-left: 0.25rem;
}

/* Schema模板样式 */
.template-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.template-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 0.125rem 0.25rem rgba(13, 110, 253, 0.25);
}

.template-item.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.template-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 1rem;
}

.template-actions {
    margin-top: 1rem;
    text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .card {
        margin: 0.5rem 0;
    }

    .task-actions {
        flex-direction: column;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    .pdf-preview-container {
        min-height: 300px;
        max-height: 400px;
    }

    .pdf-controls {
        position: static;
        transform: none;
        margin-top: 0.5rem;
        justify-content: center;
    }
}
