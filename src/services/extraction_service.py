"""信息提取服务"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

logger = logging.getLogger(__name__)

# 导入memect_insight_extractor库
import sys
import os

# 添加memect_insight_extractor库路径
memect_lib_path = os.path.join(os.path.dirname(__file__), '../../memect-insight-extractor/src')
if memect_lib_path not in sys.path:
    sys.path.insert(0, memect_lib_path)

try:
    from memect_insight_extractor import DocumentExtractor, ExtractionConfig, ExtractionRequest
    MEMECT_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入memect_insight_extractor库: {e}")
    logger.warning("信息提取功能将使用模拟模式")
    MEMECT_AVAILABLE = False
from src.models.database import Task
from src.models.schemas import ExtractionConfigRequest, TaskStatus


class ExtractionService:
    """信息提取服务"""
    
    def __init__(self):
        """初始化提取服务"""
        self.active_tasks = {}  # 存储活跃的任务
    
    async def start_extraction_task(
        self,
        task_id: str,
        markdown_content: str,
        schema: Dict[str, Any],
        config: ExtractionConfigRequest,
        db_session: AsyncSession,
        prompt_template: Optional[str] = None,
        few_shot_examples: Optional[list] = None,
        custom_instructions: Optional[str] = None
    ) -> None:
        """启动提取任务
        
        Args:
            task_id: 任务ID
            markdown_content: Markdown内容
            schema: JSON Schema
            config: 提取配置
            db_session: 数据库会话
            prompt_template: 自定义提示词模板
            few_shot_examples: 少样本示例
            custom_instructions: 自定义指令
        """
        # 创建异步任务
        task = asyncio.create_task(
            self._process_extraction(
                task_id, markdown_content, schema, config,
                db_session, prompt_template, few_shot_examples, custom_instructions
            )
        )
        
        # 存储任务引用
        self.active_tasks[task_id] = task
        
        logger.info(f"提取任务已启动: {task_id}")
    
    async def _process_extraction(
        self,
        task_id: str,
        markdown_content: str,
        schema: Dict[str, Any],
        config: ExtractionConfigRequest,
        db_session: AsyncSession,
        prompt_template: Optional[str] = None,
        few_shot_examples: Optional[list] = None,
        custom_instructions: Optional[str] = None
    ) -> None:
        """处理提取任务
        
        Args:
            task_id: 任务ID
            markdown_content: Markdown内容
            schema: JSON Schema
            config: 提取配置
            db_session: 数据库会话
            prompt_template: 自定义提示词模板
            few_shot_examples: 少样本示例
            custom_instructions: 自定义指令
        """
        start_time = time.time()
        
        try:
            # 更新任务状态为处理中
            await self._update_task_status(
                db_session, task_id, TaskStatus.PROCESSING,
                progress=10.0, message="开始信息提取"
            )

            if not MEMECT_AVAILABLE:
                # 模拟模式
                await self._simulate_extraction(
                    db_session, task_id, markdown_content, schema, start_time
                )
                return

            # 创建提取配置
            extraction_config = ExtractionConfig(
                llm_base_url=config.llm_base_url,
                llm_api_key=config.llm_api_key,
                llm_model=config.llm_model,
                llm_temperature=config.llm_temperature,
                llm_max_tokens=config.llm_max_tokens,
                llm_timeout=config.llm_timeout,
                max_retries=config.max_retries,
                retry_delay=config.retry_delay,
                max_chunk_tokens=config.max_chunk_size,
                chunk_overlap_tokens=config.chunk_overlap,
                enable_parallel=config.enable_parallel,
                max_parallel_chunks=config.max_parallel_chunks,
                debug_mode=True,
                log_level="DEBUG",
            )

            # 创建文档提取器
            extractor = DocumentExtractor(extraction_config)

            # 更新进度
            await self._update_task_status(
                db_session, task_id, TaskStatus.PROCESSING,
                progress=30.0, message="配置提取器完成"
            )

            # 创建提取请求
            extraction_request = ExtractionRequest(
                text_content=markdown_content,
                schema=schema,
                prompt_template=prompt_template,
                few_shot_examples=few_shot_examples,
                custom_instructions=custom_instructions
            )

            # 更新进度
            await self._update_task_status(
                db_session, task_id, TaskStatus.PROCESSING,
                progress=50.0, message="开始执行提取"
            )

            # 执行提取
            result = await extractor.extract_from_request(extraction_request)

            # 计算处理时间
            processing_time = time.time() - start_time

            # 更新进度
            await self._update_task_status(
                db_session, task_id, TaskStatus.PROCESSING,
                progress=90.0, message="提取完成，保存结果"
            )

            # 保存结果
            await self._save_extraction_result(
                db_session, task_id, result, processing_time
            )
            
            logger.info(f"提取任务完成: {task_id}, 耗时: {processing_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"提取任务失败: {task_id}, 错误: {str(e)}")
            await self._update_task_status(
                db_session, task_id, TaskStatus.FAILED,
                error_message=str(e), progress=0.0
            )
        finally:
            # 清理任务引用
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
    
    async def _update_task_status(
        self,
        db_session: AsyncSession,
        task_id: str,
        status: TaskStatus,
        progress: float = None,
        message: str = None,
        error_message: str = None
    ) -> None:
        """更新任务状态
        
        Args:
            db_session: 数据库会话
            task_id: 任务ID
            status: 任务状态
            progress: 进度
            message: 状态消息
            error_message: 错误消息
        """
        update_data = {"status": status.value}
        
        if progress is not None:
            update_data["progress"] = progress
        if error_message is not None:
            update_data["error_message"] = error_message
        
        stmt = update(Task).where(Task.task_id == task_id).values(**update_data)
        await db_session.execute(stmt)
        await db_session.commit()
        
        logger.debug(f"任务状态已更新: {task_id} -> {status.value}")
    
    async def _save_extraction_result(
        self,
        db_session: AsyncSession,
        task_id: str,
        result,
        processing_time: float
    ) -> None:
        """保存提取结果
        
        Args:
            db_session: 数据库会话
            task_id: 任务ID
            result: 提取结果
            processing_time: 处理时间
        """
        update_data = {
            "status": TaskStatus.COMPLETED.value,
            "progress": 100.0,
            "processing_time": processing_time,
            "extracted_data": result.extracted_data,
            "task_metadata": result.metadata
        }
        
        # 如果有错误，记录错误信息
        if result.errors:
            update_data["error_message"] = "; ".join([e.error_message for e in result.errors])
        
        stmt = update(Task).where(Task.task_id == task_id).values(**update_data)
        await db_session.execute(stmt)
        await db_session.commit()
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量"""
        return len(self.active_tasks)
    
    def is_task_active(self, task_id: str) -> bool:
        """检查任务是否活跃"""
        return task_id in self.active_tasks
