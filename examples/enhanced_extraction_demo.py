#!/usr/bin/env python3
"""增强提取功能演示示例"""

import asyncio
import os
from memect_insight_extractor import DocumentExtractor, ExtractionConfig
from memect_insight_extractor.models.extraction_request import ExtractionRequest


async def main():
    """主函数"""

    # 1. 配置提取器，启用DEBUG模式
    config = ExtractionConfig(
        llm_base_url=os.getenv("LLM_BASE_URL", "https://api.openai.com/v1"),
        llm_api_key=os.getenv("LLM_API_KEY", "your-api-key-here"),
        llm_model=os.getenv("LLM_MODEL", "gpt-4"),
        llm_temperature=0.1,
        max_retries=3,
        debug_mode=True,  # 启用DEBUG模式
        log_level="DEBUG",  # 设置日志级别
        log_file="extraction_debug.log"  # 输出到日志文件
    )
    
    # 创建提取器
    extractor = DocumentExtractor(config)
    
    # 2. 定义带主键的Schema
    schema = {
        "type": "object",
        "properties": {
            "contract_id": {
                "type": "string", 
                "description": "合同编号",
                "is_primary_key": True  # 标记为主键
            },
            "company_name": {
                "type": "string",
                "description": "公司名称"
            },
            "amount": {
                "type": "number",
                "description": "合同金额"
            },
            "date": {
                "type": "string",
                "description": "签署日期"
            },
            "status": {
                "type": "string",
                "enum": ["active", "pending", "completed"],
                "description": "合同状态"
            }
        },
        "required": ["contract_id", "company_name"]
    }
    
    # 3. 示例文档内容
    text_content = """
    合同编号：CT-2024-001
    甲方：北京科技有限公司
    乙方：上海贸易有限公司
    合同金额：100,000元
    签署日期：2024年1月15日
    合同状态：已生效
    项目内容：软件开发服务
    """
    
    # 4. 使用新功能创建提取请求
    request = ExtractionRequest(
        text_content=text_content,
        schema=schema,
        
        # 新功能1: 提取目的唯一标识
        extraction_purpose_id="contract_analysis_v2.1",
        
        # 自定义提示词模板（支持变量替换）
        prompt_template="""你是一个专业的合同信息提取专家。
你需要从合同文档中提取关键信息。

任务要求：
- 仔细阅读文档内容
- 提取以下字段：{field_names}
- 确保数据准确性和完整性
- 金额请转换为数字格式（去除货币符号）

文档ID：{document_id}
提取目的：{extraction_purpose_id}

请从合同文档中提取以下信息：
- 合同编号（contract_id）
- 公司名称（company_name）
- 合同金额（amount）- 请转换为数字
- 签署日期（date）
- 合同状态（status）- 请映射为枚举值

总共需要提取 {field_count} 个字段。
""",
        
        # 其他参数
        document_id="DOC_20240115_001",
        config_version_id="v2.1.0",
        custom_instructions="请特别注意金额格式的标准化处理"
    )
    
    print("=== 增强提取功能演示 ===")
    print(f"提取目的ID: {request.extraction_purpose_id}")
    print(f"文档ID: {request.document_id}")
    print(f"配置版本: {request.config_version_id}")
    print(f"DEBUG模式: {config.debug_mode}")
    print()
    
    # 5. 执行提取
    print("开始执行提取...")
    result = await extractor.extract_from_request(request)
    
    # 6. 输出结果
    print(f"\n=== 提取结果 ===")
    print(f"状态: {result.status}")
    print(f"处理时间: {result.processing_time:.2f}秒")
    
    if result.extracted_data is not None:
        print(f"\n提取的数据:")
        if isinstance(result.extracted_data, dict):
            for key, value in result.extracted_data.items():
                print(f"  {key}: {value}")
        elif isinstance(result.extracted_data, list):
            for i, item in enumerate(result.extracted_data):
                print(f"  [{i}]: {item}")
        else:
            print(f"  {result.extracted_data}")
    
    if result.errors:
        print(f"\n错误信息:")
        for error in result.errors:
            print(f"  {error.error_type}: {error.error_message}")
    
    print(f"\n元数据:")
    for key, value in result.metadata.items():
        print(f"  {key}: {value}")
    
    # 7. 演示Schema主键功能
    print(f"\n=== Schema主键演示 ===")
    from memect_insight_extractor.models.schema_models import SchemaField, ExtractionSchema
    
    fields = [
        SchemaField(name="contract_id", type="string", is_primary_key=True),
        SchemaField(name="company_name", type="string", required=True),
        SchemaField(name="amount", type="number"),
    ]
    
    extraction_schema = ExtractionSchema(
        name="contract_schema",
        fields=fields,
        json_schema=schema
    )
    
    print(f"主键字段: {extraction_schema.get_primary_key_fields()}")
    print(f"必需字段: {extraction_schema.get_required_fields()}")
    
    # 8. 演示变量替换功能
    print(f"\n=== 变量替换演示 ===")
    from memect_insight_extractor.core.prompt_builder import PromptBuilder
    
    builder = PromptBuilder()
    variables = builder._prepare_variables(request, text_content)
    
    print("可用变量:")
    for key, value in variables.items():
        print(f"  {key}: {value}")
    
    test_template = "文档 {document_id} 的提取目的是 ${extraction_purpose_id}，包含 {field_count} 个字段。"
    replaced_text = builder._replace_variables(test_template, variables)
    print(f"\n模板: {test_template}")
    print(f"替换后: {replaced_text}")


if __name__ == "__main__":
    asyncio.run(main())
