#!/usr/bin/env python3
"""不同Schema类型的演示示例"""

import asyncio
import os
from memect_insight_extractor import DocumentExtractor, ExtractionConfig
from memect_insight_extractor.models.extraction_request import ExtractionRequest


async def demo_object_schema():
    """演示对象类型Schema"""
    print("=== 对象类型Schema演示 ===")
    
    schema = {
        "type": "object",
        "properties": {
            "company_name": {"type": "string", "description": "公司名称"},
            "amount": {"type": "number", "description": "金额"},
            "date": {"type": "string", "description": "日期"}
        },
        "required": ["company_name"]
    }
    
    text_content = """
    合同甲方：北京科技有限公司
    合同金额：100,000元
    签署日期：2024年1月15日
    """
    
    print(f"Schema类型: {schema['type']}")
    print(f"文档内容: {text_content.strip()}")
    print(f"期望返回: 字典对象")
    print()


async def demo_array_schema():
    """演示数组类型Schema"""
    print("=== 数组类型Schema演示 ===")
    
    schema = {
        "type": "array",
        "items": {"type": "string"},
        "description": "提取文档中的所有公司名称"
    }
    
    text_content = """
    本次会议参与方包括：
    1. 北京科技有限公司
    2. 上海贸易有限公司
    3. 深圳创新科技公司
    4. 广州制造企业
    """
    
    print(f"Schema类型: {schema['type']}")
    print(f"文档内容: {text_content.strip()}")
    print(f"期望返回: 字符串数组")
    print()


async def demo_array_of_objects_schema():
    """演示对象数组类型Schema"""
    print("=== 对象数组类型Schema演示 ===")
    
    schema = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "name": {"type": "string", "description": "产品名称"},
                "price": {"type": "number", "description": "价格"},
                "quantity": {"type": "integer", "description": "数量"}
            }
        },
        "description": "提取发票中的商品列表"
    }
    
    text_content = """
    发票明细：
    1. 苹果 - 单价：5.50元 - 数量：10个
    2. 香蕉 - 单价：3.20元 - 数量：15个
    3. 橙子 - 单价：4.80元 - 数量：8个
    """
    
    print(f"Schema类型: {schema['type']}")
    print(f"文档内容: {text_content.strip()}")
    print(f"期望返回: 对象数组")
    print()


async def demo_string_schema():
    """演示字符串类型Schema"""
    print("=== 字符串类型Schema演示 ===")
    
    schema = {
        "type": "string",
        "description": "提取文档中的主要公司名称"
    }
    
    text_content = """
    本合同由北京科技有限公司与客户签署，
    涉及软件开发服务项目。
    """
    
    print(f"Schema类型: {schema['type']}")
    print(f"文档内容: {text_content.strip()}")
    print(f"期望返回: 字符串")
    print()


async def demo_number_schema():
    """演示数字类型Schema"""
    print("=== 数字类型Schema演示 ===")
    
    schema = {
        "type": "number",
        "description": "提取合同总金额"
    }
    
    text_content = """
    合同总金额：￥125,000.50元
    包含税费：￥15,000.50元
    不含税金额：￥110,000.00元
    """
    
    print(f"Schema类型: {schema['type']}")
    print(f"文档内容: {text_content.strip()}")
    print(f"期望返回: 数字")
    print()


async def demo_boolean_schema():
    """演示布尔类型Schema"""
    print("=== 布尔类型Schema演示 ===")
    
    schema = {
        "type": "boolean",
        "description": "判断合同是否已生效"
    }
    
    text_content = """
    合同状态：已生效
    生效日期：2024年1月15日
    有效期至：2025年1月14日
    """
    
    print(f"Schema类型: {schema['type']}")
    print(f"文档内容: {text_content.strip()}")
    print(f"期望返回: 布尔值")
    print()


async def run_extraction_demo(schema, text_content, description):
    """运行提取演示"""
    print(f"--- {description} ---")
    
    # 注意：这里只是演示Schema结构，实际运行需要真实的LLM API
    config = ExtractionConfig(
        llm_api_key="demo-key",  # 演示用的key
        llm_base_url="https://api.openai.com/v1",
        llm_model="gpt-4"
    )
    
    request = ExtractionRequest(
        text_content=text_content,
        schema=schema,
        prompt_template="请根据Schema定义从文档中提取信息。文档类型：{extraction_purpose_id}",
        extraction_purpose_id="schema_type_demo"
    )
    
    print(f"Schema: {schema}")
    print(f"文档: {text_content.strip()}")
    print("注意：这是演示代码，实际运行需要配置真实的LLM API")
    print()


async def main():
    """主函数"""
    print("=== 不同Schema类型支持演示 ===\n")
    
    print("本模块支持以下Schema类型的LLM返回结果解析：\n")
    
    # 演示各种Schema类型
    await demo_object_schema()
    await demo_array_schema()
    await demo_array_of_objects_schema()
    await demo_string_schema()
    await demo_number_schema()
    await demo_boolean_schema()
    
    print("=== 实际使用示例 ===")
    print("以下是实际使用时的代码结构：\n")
    
    # 对象类型示例
    await run_extraction_demo(
        schema={
            "type": "object",
            "properties": {
                "company": {"type": "string"},
                "amount": {"type": "number"}
            }
        },
        text_content="北京科技公司，金额100万元",
        description="对象类型提取"
    )
    
    # 数组类型示例
    await run_extraction_demo(
        schema={
            "type": "array",
            "items": {"type": "string"}
        },
        text_content="参与方：公司A、公司B、公司C",
        description="数组类型提取"
    )
    
    print("=== 类型处理特性 ===")
    print("1. 自动类型检测：根据Schema的type字段确定期望的返回类型")
    print("2. 智能解析：支持从JSON代码块、纯文本等多种格式中提取数据")
    print("3. 数据清洗：自动清洗货币符号、格式化数字、标准化文本等")
    print("4. 类型转换：自动将字符串转换为对应的数据类型")
    print("5. 嵌套支持：支持复杂的嵌套对象和数组结构")
    print("6. 错误处理：类型不匹配时提供详细的错误信息")


if __name__ == "__main__":
    asyncio.run(main())
