"""测试结果解析器对不同Schema类型的支持"""

import pytest

from memect_insight_extractor.core.result_parser import ResultParser


class TestResultParserTypes:
    """测试结果解析器类型支持"""

    def setup_method(self):
        """设置测试"""
        self.parser = ResultParser()

    def test_object_type_parsing(self):
        """测试对象类型解析"""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"}
            }
        }
        
        # 测试正常的JSON对象
        response = '{"name": "张三", "age": 25}'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, dict)
        assert result["name"] == "张三"
        assert result["age"] == 25

    def test_array_type_parsing(self):
        """测试数组类型解析"""
        schema = {
            "type": "array",
            "items": {"type": "string"}
        }
        
        # 测试字符串数组
        response = '["苹果", "香蕉", "橙子"]'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, list)
        assert result == ["苹果", "香蕉", "橙子"]

    def test_array_of_objects_parsing(self):
        """测试对象数组解析"""
        schema = {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "price": {"type": "number"}
                }
            }
        }
        
        response = '[{"name": "苹果", "price": 5.5}, {"name": "香蕉", "price": 3.2}]'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["name"] == "苹果"
        assert result[0]["price"] == 5.5

    def test_string_type_parsing(self):
        """测试字符串类型解析"""
        schema = {"type": "string"}
        
        # 测试带引号的字符串
        response = '"这是一个字符串"'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, str)
        assert result == "这是一个字符串"
        
        # 测试不带引号的字符串
        response = '这是一个字符串'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, str)
        assert result == "这是一个字符串"

    def test_number_type_parsing(self):
        """测试数字类型解析"""
        schema = {"type": "number"}
        
        # 测试整数
        response = '42'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, (int, float))
        assert result == 42
        
        # 测试小数
        response = '3.14'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, float)
        assert result == 3.14

    def test_integer_type_parsing(self):
        """测试整数类型解析"""
        schema = {"type": "integer"}
        
        response = '100'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, int)
        assert result == 100

    def test_boolean_type_parsing(self):
        """测试布尔类型解析"""
        schema = {"type": "boolean"}
        
        # 测试true
        response = 'true'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, bool)
        assert result is True
        
        # 测试false
        response = 'false'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert isinstance(result, bool)
        assert result is False

    def test_json_block_extraction_object(self):
        """测试从文本中提取JSON对象块"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}}
        }
        
        response = '''
        根据文档分析，提取的信息如下：
        
        ```json
        {"name": "测试公司"}
        ```
        
        以上是提取的结果。
        '''
        
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert result["name"] == "测试公司"

    def test_json_block_extraction_array(self):
        """测试从文本中提取JSON数组块"""
        schema = {
            "type": "array",
            "items": {"type": "string"}
        }
        
        response = '''
        提取的列表如下：
        
        ```json
        ["项目1", "项目2", "项目3"]
        ```
        '''
        
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert result == ["项目1", "项目2", "项目3"]

    def test_simple_type_fallback(self):
        """测试简单类型的回退解析"""
        schema = {"type": "string"}
        
        # 测试没有JSON格式的纯文本
        response = '提取的公司名称是：北京科技有限公司'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        # 应该能解析出文本，但可能有校验错误
        assert result is not None
        assert result == "提取的公司名称是：北京科技有限公司"

    def test_number_with_currency_cleaning(self):
        """测试带货币符号的数字清洗"""
        schema = {"type": "number"}
        
        response = '¥1,234.56元'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert result == 1234.56

    def test_boolean_text_parsing(self):
        """测试文本形式的布尔值解析"""
        schema = {"type": "boolean"}
        
        # 测试中文布尔值
        response = '是'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert result is True
        
        response = '否'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert result is False

    def test_type_mismatch_handling(self):
        """测试类型不匹配的处理"""
        schema = {"type": "object"}
        
        # 返回的是数组而不是对象
        response = '["item1", "item2"]'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        # 应该解析成功但有校验错误
        assert result is not None
        assert len(errors) > 0
        assert isinstance(result, list)

    def test_array_cleaning_and_normalization(self):
        """测试数组的清洗和标准化"""
        schema = {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "amount": {"type": "number"}
                }
            }
        }
        
        response = '[{"amount": "¥100.50"}, {"amount": "200元"}]'
        result, errors = self.parser.parse_and_validate(response, schema)
        
        assert result is not None
        assert len(errors) == 0
        assert result[0]["amount"] == 100.5
        assert result[1]["amount"] == 200.0
