"""测试不同Schema类型的集成"""

import pytest
from unittest.mock import AsyncMock, patch

from memect_insight_extractor import DocumentExtractor, ExtractionConfig
from memect_insight_extractor.models.extraction_request import ExtractionRequest


class TestSchemaTypeIntegration:
    """测试不同Schema类型的集成"""

    def setup_method(self):
        """设置测试"""
        self.config = ExtractionConfig(
            llm_api_key="test-key",
            debug_mode=True
        )
        self.extractor = DocumentExtractor(self.config)

    @pytest.mark.anyio
    async def test_object_schema_extraction(self):
        """测试对象类型Schema的提取"""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "amount": {"type": "number"}
            }
        }
        
        # 模拟LLM返回对象
        mock_response = '{"name": "测试公司", "amount": 100000}'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="测试文档内容",
                schema=schema
            )
            
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, dict)
            assert result.extracted_data["name"] == "测试公司"
            assert result.extracted_data["amount"] == 100000

    @pytest.mark.asyncio
    async def test_array_schema_extraction(self):
        """测试数组类型Schema的提取"""
        schema = {
            "type": "array",
            "items": {"type": "string"}
        }
        
        # 模拟LLM返回数组
        mock_response = '["公司A", "公司B", "公司C"]'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="参与方包括：公司A、公司B、公司C",
                schema=schema
            )
            
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, list)
            assert result.extracted_data == ["公司A", "公司B", "公司C"]

    @pytest.mark.asyncio
    async def test_string_schema_extraction(self):
        """测试字符串类型Schema的提取"""
        schema = {"type": "string"}
        
        # 模拟LLM返回字符串
        mock_response = '"北京科技有限公司"'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="甲方：北京科技有限公司",
                schema=schema
            )
            
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, str)
            assert result.extracted_data == "北京科技有限公司"

    @pytest.mark.asyncio
    async def test_number_schema_extraction(self):
        """测试数字类型Schema的提取"""
        schema = {"type": "number"}
        
        # 模拟LLM返回数字
        mock_response = '125000.50'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="合同金额：125,000.50元",
                schema=schema
            )
            
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, (int, float))
            assert result.extracted_data == 125000.50

    @pytest.mark.asyncio
    async def test_boolean_schema_extraction(self):
        """测试布尔类型Schema的提取"""
        schema = {"type": "boolean"}
        
        # 模拟LLM返回布尔值
        mock_response = 'true'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="合同状态：已生效",
                schema=schema
            )
            
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, bool)
            assert result.extracted_data is True

    @pytest.mark.asyncio
    async def test_array_of_objects_extraction(self):
        """测试对象数组类型Schema的提取"""
        schema = {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "price": {"type": "number"}
                }
            }
        }
        
        # 模拟LLM返回对象数组
        mock_response = '[{"name": "苹果", "price": 5.5}, {"name": "香蕉", "price": 3.2}]'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="商品：苹果 5.5元，香蕉 3.2元",
                schema=schema
            )
            
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, list)
            assert len(result.extracted_data) == 2
            assert result.extracted_data[0]["name"] == "苹果"
            assert result.extracted_data[0]["price"] == 5.5

    @pytest.mark.asyncio
    async def test_debug_mode_with_different_types(self):
        """测试DEBUG模式下不同类型的日志输出"""
        schema = {"type": "array", "items": {"type": "string"}}
        mock_response = '["项目1", "项目2"]'
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = mock_response
            
            request = ExtractionRequest(
                text_content="项目列表：项目1、项目2",
                schema=schema
            )
            
            # 这个测试主要验证DEBUG模式不会因为类型问题而崩溃
            result = await self.extractor.extract_from_request(request)
            
            assert result.is_success()
            assert isinstance(result.extracted_data, list)

    @pytest.mark.asyncio
    async def test_merge_results_with_arrays(self):
        """测试数组类型结果的合并"""
        # 这个测试需要模拟文本分段的情况
        schema = {
            "type": "array",
            "items": {"type": "string"}
        }
        
        # 模拟长文档需要分段
        long_text = "公司列表：" + "、".join([f"公司{i}" for i in range(1, 100)])
        
        # 模拟每个块返回部分结果
        mock_responses = ['["公司1", "公司2"]', '["公司3", "公司4"]']
        
        with patch.object(self.extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
            mock_llm.side_effect = mock_responses
            
            # 强制使用分段处理
            with patch.object(self.extractor.text_splitter, 'should_split', return_value=True):
                with patch.object(self.extractor.text_splitter, 'split_text') as mock_split:
                    # 模拟分成两个块
                    from memect_insight_extractor.core.text_splitter import TextChunk
                    mock_split.return_value = [
                        TextChunk(content="公司1、公司2", chunk_index=0),
                        TextChunk(content="公司3、公司4", chunk_index=1)
                    ]
                    
                    request = ExtractionRequest(
                        text_content=long_text,
                        schema=schema
                    )
                    
                    result = await self.extractor.extract_from_request(request)
                    
                    assert result.is_success()
                    assert isinstance(result.extracted_data, list)
                    # 验证结果被正确合并
                    assert "公司1" in result.extracted_data
                    assert "公司2" in result.extracted_data
                    assert "公司3" in result.extracted_data
                    assert "公司4" in result.extracted_data
