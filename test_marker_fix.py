#!/usr/bin/env python3
"""测试marker修复的脚本"""

import sys
import os
sys.path.append('/Users/<USER>/Projects/memect_insight_extractor_preview')

from src.services.pdf_processor import PDFProcessor
import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_marker_fix():
    """测试marker修复"""
    processor = PDFProcessor()

    # 使用真实的PDF文件测试
    test_pdf_path = "/Users/<USER>/Projects/memect_insight_extractor_preview/uploads/001872_20230620_J7BC_1-30.pdf"

    if not os.path.exists(test_pdf_path):
        logger.warning(f"PDF文件不存在: {test_pdf_path}")
        logger.info("改为测试模拟模式...")

        # 强制测试模拟模式
        import src.services.pdf_processor as pdf_module
        original_marker_available = pdf_module.MARKER_AVAILABLE

        try:
            pdf_module.MARKER_AVAILABLE = False
            markdown_content = processor._convert_pdf_sync("test.pdf")
        finally:
            pdf_module.MARKER_AVAILABLE = original_marker_available
    else:
        logger.info("开始测试真实PDF转换...")
        markdown_content = await processor.convert_pdf_to_markdown(test_pdf_path)

    logger.info(f"转换成功！内容类型: {type(markdown_content)}")
    logger.info(f"内容长度: {len(markdown_content)} 字符")
    logger.info(f"内容预览: {markdown_content[:200]}...")

    # 检查是否是字符串类型
    if isinstance(markdown_content, str) and len(markdown_content) > 0:
        return True
    else:
        logger.error(f"返回内容类型错误: {type(markdown_content)}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_marker_fix())
    if result:
        print("✅ 测试通过：marker修复成功")
    else:
        print("❌ 测试失败：marker修复有问题")
